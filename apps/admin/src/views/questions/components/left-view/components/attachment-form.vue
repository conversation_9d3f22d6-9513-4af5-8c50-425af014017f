<script setup lang="ts">
import type { UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui'
import { useNaiveForm } from '@sa/hooks'
import { HuaweiOBSUploader } from '@sa/utils'

defineOptions({
  name: 'AttachmentForm',
})

const modelInfo = defineModel('modelInfo', {
  type: Object as PropType<{
    FileUrls: string[]
    AdditionalRequirements: string
  }>,
  default: () => ({
    FileUrls: [],
    AdditionalRequirements: '',
  }),
})

// 表单引用和校验
const { formRef, validate } = useNaiveForm()

// 文件上传组件引用
const fileUploadRef = ref()

// 文件列表
const fileList = ref<UploadFileInfo[]>([])

// 华为云OBS上传器实例
const obsUploader = new HuaweiOBSUploader({})

// 表单校验规则
const rules = {
  FileUrls: [
    {
      required: true,
      message: '请上传附件文件',
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: any[]) => {
        if (!value || value.length === 0) {
          return new Error('请上传附件文件')
        }
        return true
      },
    },
  ],
}

// 监听外部值变化，同步到文件列表
watch(
  () => modelInfo.value.FileUrls,
  (newUrls) => {
    if (newUrls && newUrls.length > 0) {
      fileList.value = newUrls.map((url, index) => ({
        id: `file-${index}`,
        name: getFileNameFromUrl(url),
        status: 'finished',
        url,
      }))
    }
    else {
      fileList.value = []
    }
  },
  { immediate: true },
)

// 从URL中提取文件名
function getFileNameFromUrl(url: string): string {
  const parts = url.split('/')
  return parts[parts.length - 1] || 'unknown'
}

// 文件上传前的检查
function handleBeforeUpload(data: { file: UploadFileInfo, fileList: UploadFileInfo[] }): boolean {
  console.log(789)
  const { file } = data

  // 检查文件大小 (20MB)
  if (file.file && file.file.size > 20 * 1024 * 1024) {
    window.$message?.error('文件大小不能超过 20MB')
    return false
  }

  // 检查文件数量
  if (fileList.value.length >= 3) {
    window.$message?.error('最多只能上传 3 个文件')
    return false
  }

  return true
}

// 自定义上传处理（使用华为云OBS）
async function handleCustomRequest(options: UploadCustomRequestOptions) {
  const { file, onProgress, onFinish, onError } = options

  try {
    // 使用华为云OBS上传
    const result = await obsUploader.upload({
      file: file.file!,
      onProgress: (progress: number) => {
        onProgress({ percent: progress })
      },
    })
    console.log(result)
    // 更新文件信息
    file.url = result.url
    file.status = 'finished'

    // 通知上传完成
    onFinish()

    // 更新模型值
    updateModelValue()

    window.$message?.success('文件上传成功')
  }
  catch (error) {
    console.error('华为云OBS上传失败:', error)

    // 设置文件状态为错误
    file.status = 'error'

    // 通知上传失败
    onError()

    window.$message?.error('文件上传失败')
  }
}

// 文件列表变化
function handleFileListChange(newFileList: UploadFileInfo[]) {
  fileList.value = newFileList
  updateModelValue()
}

// 文件移除
function handleRemove(_options: { file: UploadFileInfo, fileList: UploadFileInfo[] }) {
  updateModelValue()
  return true
}

// 更新模型值
function updateModelValue() {
  const urls = fileList.value
    .filter(file => file.status === 'finished' && file.url)
    .map(file => file.url!)
  modelInfo.value.FileUrls = urls
}

// 暴露校验方法给父组件
defineExpose({
  validate,
})
</script>

<template>
  <div class="p-12px">
    <NForm ref="formRef" :model="modelInfo" :rules="rules">
      <!-- 出题范围标题 -->
      <div class="mb-16px flex items-center">
        <SvgIcon icon="mdi:file-document-outline" class="mr-8px text-20px text-blue-500" />
        <span class="text-14px text-[#333] font-500">出题范围</span>
      </div>

      <!-- 文件上传 -->
      <NFormItem path="FileUrls">
        <template #label>
          <span class="text-left text-14px text-[#464646] font-500">上传附件</span>
        </template>
        <NUpload
          ref="fileUploadRef"
          v-model:file-list="fileList"
          :multiple="true"
          :max="3"
          accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
          list-type="text"
          directory-dnd
          :custom-request="handleCustomRequest"
          @before-upload="handleBeforeUpload"
          @update:file-list="handleFileListChange"
          @remove="handleRemove"
        >
          <NUploadDragger>
            <div class="mb-12px flex items-center justify-center gap-8px">
              <SvgIcon icon="mdi:cloud-upload" class="text-48px text-gray-400" />
              <NText class="text-16px">
                点击或者拖动文件到该区域来上传
              </NText>
            </div>

            <NText depth="3" class="mt-8px text-12px">
              最多上传 3 个文件
            </NText>
            <NText depth="3" class="text-12px">
              支持上传图片、Word、PDF 格式文件，单个文件大小限制 20MB 以内
            </NText>
            <NText depth="3" class="text-12px">
              文件解析需耗时，提交后请耐心等待，勿重复操作
            </NText>
          </NUploadDragger>
        </NUpload>
      </NFormItem>

      <!-- 补充内容区域 -->
      <div class="border border-gray-200 rounded-8px bg-white">
        <!-- 标题栏 -->
        <div class="flex items-center justify-between p-16px">
          <span class="text-14px text-[#464646] font-500">补充内容（选填）</span>
        </div>

        <!-- 可折叠内容 -->
        <div class="overflow-hidden transition-all duration-300">
          <div class="border-t border-gray-200 p-16px">
            <NInput
              v-model:value="modelInfo.AdditionalRequirements"
              type="textarea"
              placeholder="您可以补充特殊的出题要求。&#10;示例如下：&#10;题目中需要包含&quot;折射&quot;&quot;反射&quot;这两个词汇"
              :rows="4"
              clearable
              maxlength="500"
              show-count
              class="w-full"
            />
          </div>
        </div>
      </div>
    </NForm>
  </div>
</template>
