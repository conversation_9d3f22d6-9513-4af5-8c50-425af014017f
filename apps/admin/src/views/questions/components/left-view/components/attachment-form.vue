<script setup lang="ts">
import { useNaiveForm } from '@sa/hooks'

defineOptions({
  name: 'AttachmentForm',
})

const modelInfo = defineModel('modelInfo', {
  type: Object as PropType<{
    FileUrls: string[]
    AdditionalRequirements: string
  }>,
  default: () => ({
    FileUrls: [],
    AdditionalRequirements: '',
  }),
})

// 表单引用和校验
const { formRef, validate } = useNaiveForm()

// 表单校验规则
const rules = {
  FileUrl: [
    {
      required: true,
      message: '请上传附件文件',
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: any[]) => {
        if (!value || value.length === 0) {
          return new Error('请上传附件文件')
        }
        return true
      },
    },
  ],
}

// 暴露校验方法给父组件
defineExpose({
  validate,
})

// 处理文件上传
function handleFileChange(fileList: any[]) {
  // 更新文件列表到模型中
  modelInfo.value.FileUrls = fileList.map(file => file.url || file.name)
}

// 处理文件移除
function handleFileRemove(index: number) {
  modelInfo.value.FileUrls.splice(index, 1)
}
</script>

<template>
  <div class="p-12px">
    <NForm ref="formRef" :model="modelInfo" :rules="rules">
      <!-- 出题范围标题 -->
      <div class="mb-16px flex items-center">
        <SvgIcon icon="mdi:file-document-outline" class="mr-8px text-20px text-blue-500" />
        <span class="text-14px text-[#333] font-500">出题范围</span>
      </div>

      <!-- 文件上传 -->
      <NFormItem path="FileUrl">
        <template #label>
          <span class="text-left text-14px text-[#464646] font-500">上传附件</span>
        </template>
        <NUpload
          multiple
          :max="3"
          directory-dnd
          accept=".pdf,.doc,.docx,.txt"
          @update:file-list="handleFileChange"
        >
          <NUploadDragger>
            <div class="mb-12px flex items-center justify-center gap-8px">
              <SvgIcon icon="mdi:cloud-upload" class="text-48px text-gray-400" />
              <NText class="text-16px">
                点击或者拖动文件到该区域来上传
              </NText>
            </div>

            <p depth="3" class="">
              最多上传 3 个文件
            </p>
            <p depth="3" class="">
              支持上传图片、Word、PDF 格式文件，单个文件大小限制 20MB 以内
            </p>
            <p depth="3" class="">
              文件解析需耗时，提交后请耐心等待，勿重复操作
            </p>
          </NUploadDragger>
        </NUpload>
      </NFormItem>

      <!-- 已上传文件列表 -->
      <div v-if="modelInfo.FileUrls.length > 0" class="mb-16px">
        <div class="mb-8px text-14px text-[#464646] font-500">
          已上传文件：
        </div>
        <div class="space-y-8px">
          <div
            v-for="(fileName, index) in modelInfo.FileUrls"
            :key="index"
            class="flex items-center justify-between rounded-6px bg-gray-50 p-8px"
          >
            <div class="flex items-center gap-8px">
              <SvgIcon icon="mdi:file-document" class="text-16px text-blue-500" />
              <span class="text-14px">{{ fileName }}</span>
            </div>
            <NButton
              size="small"
              quaternary
              type="error"
              @click="handleFileRemove(index)"
            >
              <SvgIcon icon="mdi:close" />
            </NButton>
          </div>
        </div>
      </div>

      <!-- 补充内容区域 -->
      <div class="border border-gray-200 rounded-8px bg-white">
        <!-- 标题栏 -->
        <div class="flex items-center justify-between p-16px">
          <span class="text-14px text-[#464646] font-500">补充内容（选填）</span>
        </div>

        <!-- 可折叠内容 -->
        <div class="overflow-hidden transition-all duration-300">
          <div class="border-t border-gray-200 p-16px">
            <NInput
              v-model:value="modelInfo.AdditionalRequirements"
              type="textarea"
              placeholder="您可以补充特殊的出题要求。&#10;示例如下：&#10;题目中需要包含&quot;折射&quot;&quot;反射&quot;这两个词汇"
              :rows="4"
              clearable
              maxlength="500"
              show-count
              class="w-full"
            />
          </div>
        </div>
      </div>
    </NForm>
  </div>
</template>
