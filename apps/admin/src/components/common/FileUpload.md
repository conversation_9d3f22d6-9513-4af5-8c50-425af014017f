# FileUpload 文件上传组件

基于 Naive UI 的 n-upload 组件二次封装，集成华为云 OBS 上传功能。

## 特性

- 🚀 基于华为云 OBS 的文件上传
- 📁 支持拖拽上传和点击上传
- 🔄 实时上传进度显示
- 📊 文件大小和数量限制
- 🎨 多种显示模式（文本、图片、图片卡片）
- ✅ 完整的事件回调
- 🛡️ TypeScript 支持

## 基础用法

```vue
<template>
  <FileUpload
    v-model="fileUrls"
    :multiple="true"
    :max="3"
    :max-size="20"
    accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
    @success="handleUploadSuccess"
    @error="handleUploadError"
  />
</template>

<script setup lang="ts">
import FileUpload from '@/components/common/FileUpload.vue'

const fileUrls = ref<string[]>([])

function handleUploadSuccess(file: any, response: any) {
  console.log('上传成功:', file, response)
}

function handleUploadError(file: any, error: any) {
  console.error('上传失败:', file, error)
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | `string[]` | `[]` | 文件URL列表，支持v-model |
| multiple | `boolean` | `true` | 是否支持多选 |
| max | `number` | `5` | 最大文件数量 |
| maxSize | `number` | `20` | 最大文件大小(MB) |
| accept | `string` | `'.pdf,.doc,.docx,.txt,.jpg,.jpeg,.png'` | 接受的文件类型 |
| disabled | `boolean` | `false` | 是否禁用 |
| showFileList | `boolean` | `true` | 是否显示文件列表 |
| listType | `'text' \| 'image' \| 'image-card'` | `'text'` | 文件列表类型 |
| dragUpload | `boolean` | `true` | 是否支持拖拽上传 |
| uploadText | `string` | `'点击上传'` | 上传按钮文案 |
| dragText | `string` | `'点击或者拖动文件到该区域来上传'` | 拖拽提示文案 |
| limitText | `string` | `''` | 限制说明文案（为空时自动生成） |
| height | `string` | `'auto'` | 上传区域高度 |
| width | `string` | `'100%'` | 上传区域宽度 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `(value: string[])` | 文件URL列表更新 |
| change | `(fileList: UploadFileInfo[])` | 文件列表变化 |
| success | `(file: UploadFileInfo, response: any)` | 文件上传成功 |
| error | `(file: UploadFileInfo, error: any)` | 文件上传失败 |
| remove | `(file: UploadFileInfo)` | 文件移除 |
| exceed | `(files: File[])` | 超出文件数量限制 |

## Methods

通过 ref 可以调用以下方法：

| 方法名 | 参数 | 说明 |
|--------|------|------|
| clearFiles | `()` | 清空文件列表 |
| submit | `()` | 手动触发上传 |

## 使用示例

### 拖拽上传

```vue
<FileUpload
  v-model="fileUrls"
  :multiple="true"
  :max="3"
  :max-size="20"
  accept=".pdf,.doc,.docx,.txt"
  :drag-upload="true"
  drag-text="点击或拖拽文件到此处上传"
  limit-text="最多上传3个文件，支持PDF、Word、TXT格式，单个文件不超过20MB"
/>
```

### 按钮上传

```vue
<FileUpload
  v-model="fileUrls"
  :multiple="false"
  :max="1"
  :drag-upload="false"
  upload-text="选择文件"
/>
```

### 图片卡片模式

```vue
<FileUpload
  v-model="imageUrls"
  :multiple="true"
  :max="5"
  accept=".jpg,.jpeg,.png,.gif"
  list-type="image-card"
/>
```

### 表单中使用

```vue
<template>
  <NForm ref="formRef" :model="formData" :rules="rules">
    <NFormItem path="attachments" label="附件">
      <FileUpload
        v-model="formData.attachments"
        :multiple="true"
        :max="3"
        @success="handleUploadSuccess"
        @error="handleUploadError"
      />
    </NFormItem>
  </NForm>
</template>

<script setup lang="ts">
const formData = ref({
  attachments: []
})

const rules = {
  attachments: [
    {
      required: true,
      message: '请上传附件',
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: string[]) => {
        if (!value || value.length === 0) {
          return new Error('请上传附件')
        }
        return true
      }
    }
  ]
}
</script>
```

## 注意事项

1. 组件使用华为云 OBS 进行文件上传，需要确保环境变量配置正确
2. 文件上传是异步的，成功后会自动更新 modelValue
3. 支持的文件类型通过 accept 属性控制
4. 文件大小限制通过 maxSize 属性控制（单位：MB）
5. 上传进度会自动显示，无需额外处理

## 环境配置

确保在 `.env` 文件中配置华为云 OBS 相关参数：

```env
VITE_OBS_ACCESS_KEY_ID=your_access_key_id
VITE_OBS_SECRET_ACCESS_KEY=your_secret_access_key
```
