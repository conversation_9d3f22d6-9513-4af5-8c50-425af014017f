<script setup lang="ts">
import FileUpload from './FileUpload.vue'

// 文件URL列表
const fileUrls = ref<string[]>([])

// 处理文件变化
function handleFileChange(fileList: any[]) {
  console.log('文件列表变化:', fileList)
}

// 处理上传成功
function handleUploadSuccess(file: any, response: any) {
  console.log('文件上传成功:', file, response)
}

// 处理上传失败
function handleUploadError(file: any, error: any) {
  console.error('文件上传失败:', file, error)
}

// 处理文件移除
function handleFileRemove(file: any) {
  console.log('文件移除:', file)
}

// 处理超出限制
function handleExceed(files: File[]) {
  console.log('超出文件数量限制:', files)
}
</script>

<template>
  <div class="p-20px">
    <h2 class="mb-20px text-18px font-bold">FileUpload 组件使用示例</h2>
    
    <!-- 基础用法 -->
    <div class="mb-40px">
      <h3 class="mb-16px text-16px font-medium">基础用法（拖拽上传）</h3>
      <FileUpload
        v-model="fileUrls"
        :multiple="true"
        :max="3"
        :max-size="20"
        accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
        :drag-upload="true"
        drag-text="点击或者拖动文件到该区域来上传"
        limit-text="最多上传 3 个文件，支持上传图片、Word、PDF 格式文件，单个文件大小限制 20MB 以内"
        @change="handleFileChange"
        @success="handleUploadSuccess"
        @error="handleUploadError"
        @remove="handleFileRemove"
        @exceed="handleExceed"
      />
      
      <div v-if="fileUrls.length > 0" class="mt-16px">
        <h4 class="mb-8px text-14px font-medium">已上传文件：</h4>
        <div class="space-y-4px">
          <div
            v-for="(url, index) in fileUrls"
            :key="index"
            class="text-12px text-gray-600"
          >
            {{ index + 1 }}. {{ url }}
          </div>
        </div>
      </div>
    </div>

    <!-- 按钮上传 -->
    <div class="mb-40px">
      <h3 class="mb-16px text-16px font-medium">按钮上传</h3>
      <FileUpload
        v-model="fileUrls"
        :multiple="false"
        :max="1"
        :max-size="10"
        accept=".jpg,.jpeg,.png"
        :drag-upload="false"
        upload-text="选择图片"
        @change="handleFileChange"
        @success="handleUploadSuccess"
        @error="handleUploadError"
      />
    </div>

    <!-- 图片卡片模式 -->
    <div class="mb-40px">
      <h3 class="mb-16px text-16px font-medium">图片卡片模式</h3>
      <FileUpload
        v-model="fileUrls"
        :multiple="true"
        :max="5"
        :max-size="5"
        accept=".jpg,.jpeg,.png,.gif"
        list-type="image-card"
        :drag-upload="true"
        drag-text="上传图片"
        @change="handleFileChange"
        @success="handleUploadSuccess"
        @error="handleUploadError"
      />
    </div>

    <!-- 禁用状态 -->
    <div class="mb-40px">
      <h3 class="mb-16px text-16px font-medium">禁用状态</h3>
      <FileUpload
        v-model="fileUrls"
        :disabled="true"
        drag-text="上传已禁用"
      />
    </div>

    <!-- 自定义样式 -->
    <div class="mb-40px">
      <h3 class="mb-16px text-16px font-medium">自定义样式</h3>
      <FileUpload
        v-model="fileUrls"
        height="200px"
        width="400px"
        drag-text="自定义尺寸的上传区域"
        @change="handleFileChange"
        @success="handleUploadSuccess"
        @error="handleUploadError"
      />
    </div>
  </div>
</template>

<style scoped>
/* 示例页面样式 */
</style>
