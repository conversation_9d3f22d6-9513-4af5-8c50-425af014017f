<script setup lang="ts">
import type { UploadCustomRequestOptions, UploadFileInfo, UploadInst } from 'naive-ui'
import { HuaweiOBSUploader } from '@sa/utils'

defineOptions({
  name: 'FileUpload',
})

const props = withDefaults(defineProps<FileUploadProps>(), {
  modelValue: () => [],
  multiple: true,
  max: 5,
  maxSize: 20,
  accept: '.pdf,.doc,.docx,.txt,.jpg,.jpeg,.png',
  disabled: false,
  showFileList: true,
  listType: 'text',
  dragUpload: true,
  uploadText: '点击上传',
  dragText: '点击或者拖动文件到该区域来上传',
  height: 'auto',
  width: '100%',
})

const emit = defineEmits<{
  'update:modelValue': [value: string[]]
  'change': [fileList: UploadFileInfo[]]
  'success': [file: UploadFileInfo, response: any]
  'error': [file: UploadFileInfo, error: any]
  'remove': [file: UploadFileInfo]
  'exceed': [files: File[]]
}>()

interface FileUploadProps {
  // 基础配置
  modelValue?: string[] // 文件URL列表
  multiple?: boolean // 是否支持多选
  max?: number // 最大文件数量
  maxSize?: number // 最大文件大小(MB)
  accept?: string // 接受的文件类型
  disabled?: boolean // 是否禁用

  // 上传配置
  action?: string // 上传地址
  headers?: Record<string, string> // 请求头
  data?: Record<string, any> // 额外数据

  // 显示配置
  showFileList?: boolean // 是否显示文件列表
  listType?: 'text' | 'image' | 'image-card' // 文件列表类型
  dragUpload?: boolean // 是否支持拖拽上传

  // 提示文案
  uploadText?: string // 上传按钮文案
  dragText?: string // 拖拽提示文案
  limitText?: string // 限制说明文案

  // 自定义样式
  height?: string // 上传区域高度
  width?: string // 上传区域宽度
}

// 上传组件引用
const uploadRef = ref<UploadInst>()

// 文件列表
const fileList = ref<UploadFileInfo[]>([])

// 华为云OBS上传器实例
const obsUploader = new HuaweiOBSUploader({})

// 计算属性
const uploadStyle = computed(() => ({
  height: props.height,
  width: props.width,
}))

const limitTextComputed = computed(() => {
  if (props.limitText)
    return props.limitText
  const sizeText = `单个文件大小限制 ${props.maxSize}MB 以内`
  const countText = props.multiple ? `最多上传 ${props.max} 个文件` : '单个文件上传'
  const typeText = `支持格式：${props.accept}`
  return `${countText}，${sizeText}，${typeText}`
})

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      fileList.value = newValue.map((url, index) => ({
        id: `file-${index}`,
        name: getFileNameFromUrl(url),
        status: 'finished',
        url,
      }))
    }
    else {
      fileList.value = []
    }
  },
  { immediate: true },
)

// 从URL中提取文件名
function getFileNameFromUrl(url: string): string {
  const parts = url.split('/')
  return parts[parts.length - 1] || 'unknown'
}

// 文件上传前的检查
function beforeUpload(data: { file: UploadFileInfo, fileList: UploadFileInfo[] }): boolean {
  const { file } = data

  // 检查文件大小
  if (file.file && file.file.size > props.maxSize * 1024 * 1024) {
    window.$message?.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }

  // 检查文件数量
  if (props.multiple && fileList.value.length >= props.max) {
    window.$message?.error(`最多只能上传 ${props.max} 个文件`)
    emit('exceed', [file.file!])
    return false
  }

  return true
}

// 自定义上传处理（使用华为云OBS）
async function handleCustomRequest(options: UploadCustomRequestOptions) {
  const { file, onProgress, onFinish, onError } = options
  try {
    // 使用华为云OBS上传
    const result = await obsUploader.upload({
      file: file.file!,
      onProgress: (progress: number) => {
        onProgress({ percent: progress })
      },
      acl: 'public-read',
      metadata: {
        'uploaded-by': 'file-upload-component',
        'upload-time': new Date().toISOString(),
      },
    })

    // 更新文件信息
    file.url = result.url
    file.status = 'finished'

    // 通知上传完成
    onFinish()

    // 更新模型值
    updateModelValue()

    // 触发成功事件
    emit('success', file, result)

    window.$message?.success('文件上传成功')
  }
  catch (error) {
    console.error('华为云OBS上传失败:', error)

    // 设置文件状态为错误
    file.status = 'error'

    // 通知上传失败
    onError()

    // 触发错误事件
    emit('error', file, error)

    window.$message?.error('文件上传失败')
  }
}

// 文件列表变化
function handleFileListChange(fileList: UploadFileInfo[]) {
  updateModelValue()
  emit('change', fileList)
}

// 文件移除
function handleRemove(options: { file: UploadFileInfo, fileList: UploadFileInfo[] }) {
  const { file } = options
  emit('remove', file)
  updateModelValue()
  return true
}

// 更新模型值
function updateModelValue() {
  const urls = fileList.value
    .filter(file => file.status === 'finished' && file.url)
    .map(file => file.url!)
  emit('update:modelValue', urls)
}

// 清空文件列表
function clearFiles() {
  fileList.value = []
  updateModelValue()
}

// 手动上传
function submit() {
  uploadRef.value?.submit()
}

// 暴露方法
defineExpose({
  clearFiles,
  submit,
  uploadRef,
})
</script>

<template>
  <div class="file-upload" :style="uploadStyle">
    <NUpload
      ref="uploadRef"
      v-model:file-list="fileList"
      :multiple="multiple"
      :max="max"
      :accept="accept"
      :disabled="disabled"
      :show-file-list="showFileList"
      :list-type="listType"
      :directory-dnd="dragUpload"
      :custom-request="handleCustomRequest"
      @before-upload="beforeUpload"
      @update:file-list="handleFileListChange"
      @remove="handleRemove"
    >
      <template v-if="dragUpload">
        <NUploadDragger>
          <div class="upload-dragger-content">
            <div class="mb-12px flex items-center justify-center">
              <SvgIcon icon="mdi:cloud-upload" class="text-48px text-gray-400" />
            </div>
            <NText class="text-16px">
              {{ dragText }}
            </NText>
            <p class="mt-8px text-12px text-gray-500">
              {{ limitTextComputed }}
            </p>
          </div>
        </NUploadDragger>
      </template>

      <template v-else>
        <NButton :disabled="disabled">
          <template #icon>
            <SvgIcon icon="mdi:upload" />
          </template>
          {{ uploadText }}
        </NButton>
      </template>
    </NUpload>
  </div>
</template>

<style scoped>
.file-upload {
  width: 100%;
}

.upload-dragger-content {
  padding: 20px;
  text-align: center;
}

:deep(.n-upload-dragger) {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: border-color 0.3s;
}

:deep(.n-upload-dragger:hover) {
  border-color: #40a9ff;
}

:deep(.n-upload-file-list) {
  margin-top: 16px;
}
</style>
